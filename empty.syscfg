/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.0+4000"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12         = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121        = ADC12.addInstance();
const ADC122        = ADC12.addInstance();
const DAC12         = scripting.addModule("/ti/driverlib/DAC12", {}, false);
const DAC121        = DAC12.addInstance();
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const SPI           = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1          = SPI.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER         = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1        = TIMER.addInstance();
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.inputFreq    = 40;
pinFunction4.enable       = true;
pinFunction4.HFCLKMonitor = true;
pinFunction4.HFXTStartup  = 20;

ADC121.$name                             = "ADC12_0";
ADC121.trigSrc                           = "DL_ADC12_TRIG_SRC_EVENT";
ADC121.adcMem0trig                       = "DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                       = "10us";
ADC121.enabledInterrupts                 = ["DL_ADC12_INTERRUPT_DMA_DONE"];
ADC121.interruptPriority                 = "0";
ADC121.configureDMA                      = true;
ADC121.sampCnt                           = 1;
ADC121.enabledDMATriggers                = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC121.repeatMode                        = true;
ADC121.subChanID                         = 2;
ADC121.peripheral.$assign                = "ADC0";
ADC121.peripheral.adcPin0.$assign        = "PA27";
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric0";
ADC121.DMA_CHANNEL.$name                 = "DMA_CH0";
ADC121.DMA_CHANNEL.addressMode           = "f2b";
ADC121.DMA_CHANNEL.srcLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.dstLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.configureTransferSize = true;
ADC121.DMA_CHANNEL.transferSize          = 1024;
ADC121.DMA_CHANNEL.transferMode          = "FULL_CH_REPEAT_SINGLE";

ADC122.$name                             = "ADC12_1";
ADC122.adcMem0trig                       = "DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT";
ADC122.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC122.sampleTime0                       = "10us";
ADC122.enabledInterrupts                 = ["DL_ADC12_INTERRUPT_DMA_DONE"];
ADC122.interruptPriority                 = "0";
ADC122.configureDMA                      = true;
ADC122.sampCnt                           = 1;
ADC122.enabledDMATriggers                = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC122.repeatMode                        = true;
ADC122.trigSrc                           = "DL_ADC12_TRIG_SRC_EVENT";
ADC122.subChanID                         = 1;
ADC122.peripheral.$assign                = "ADC1";
ADC122.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric1";
ADC122.DMA_CHANNEL.$name                 = "DMA_CH1";
ADC122.DMA_CHANNEL.addressMode           = "f2b";
ADC122.DMA_CHANNEL.srcLength             = "HALF_WORD";
ADC122.DMA_CHANNEL.dstLength             = "HALF_WORD";
ADC122.DMA_CHANNEL.configureTransferSize = true;
ADC122.DMA_CHANNEL.transferSize          = 1024;
ADC122.DMA_CHANNEL.transferMode          = "FULL_CH_REPEAT_SINGLE";

DAC121.$name                             = "DAC12_0";
DAC121.dacOutputPinEn                    = true;
DAC121.dacAmplifier                      = "ON";
DAC121.dacPosVREF                        = "VEREFP";
DAC121.dacNegVREF                        = "VEREFN";
DAC121.dacSampleTimerEn                  = false;
DAC121.peripheral.$assign                = "DAC0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                          = "LED";
GPIO1.associatedPins[0].$name        = "PIN";
GPIO1.associatedPins[0].ioStructure  = "SD";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "22";
GPIO1.associatedPins[0].pin.$assign  = "PB22";

GPIO2.$name                         = "LCD";
GPIO2.port                          = "PORTB";
GPIO2.associatedPins.create(4);
GPIO2.associatedPins[0].ioStructure = "SD";
GPIO2.associatedPins[0].assignedPin = "10";
GPIO2.associatedPins[0].$name       = "RES";
GPIO2.associatedPins[1].$name       = "DC";
GPIO2.associatedPins[1].ioStructure = "SD";
GPIO2.associatedPins[1].assignedPin = "11";
GPIO2.associatedPins[1].pin.$assign = "PB11";
GPIO2.associatedPins[2].$name       = "CS";
GPIO2.associatedPins[2].ioStructure = "SD";
GPIO2.associatedPins[2].assignedPin = "14";
GPIO2.associatedPins[2].pin.$assign = "PB14";
GPIO2.associatedPins[3].$name       = "BLK";
GPIO2.associatedPins[3].ioStructure = "SD";
GPIO2.associatedPins[3].assignedPin = "26";
GPIO2.associatedPins[3].pin.$assign = "PB26";

GPIO3.$name                   = "AD_9850PINS";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name = "P_W_CLK";
GPIO3.associatedPins[1].$name = "DATA";
GPIO3.associatedPins[2].$name = "FQ_UD";
GPIO3.associatedPins[3].$name = "RESET";

const GPIO4         = GPIO.addInstance();
GPIO4.$name                   = "BUTTONS";
GPIO4.port                    = "PORTB";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name = "FREQ_UP";
GPIO4.associatedPins[0].direction = "INPUT";
GPIO4.associatedPins[0].internalResistor = "PULL_UP";
GPIO4.associatedPins[0].interruptEn = true;
GPIO4.associatedPins[0].interruptPriority = "2";
GPIO4.associatedPins[0].polarity = "FALL";
GPIO4.associatedPins[1].$name = "FREQ_DOWN";
GPIO4.associatedPins[1].direction = "INPUT";
GPIO4.associatedPins[1].internalResistor = "PULL_UP";
GPIO4.associatedPins[1].interruptEn = true;
GPIO4.associatedPins[1].interruptPriority = "2";
GPIO4.associatedPins[1].polarity = "FALL";

SPI1.frameFormat                        = "MOTO3";
SPI1.targetBitRate                      = 16000000;
SPI1.$name                              = "TFTspi";
SPI1.sclkPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
SPI1.sclkPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.sclkPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.sclkPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.mosiPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
SPI1.mosiPinConfig.direction            = scripting.forceWrite("OUTPUT");
SPI1.mosiPinConfig.hideOutputInversion  = scripting.forceWrite(false);
SPI1.mosiPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.mosiPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.misoPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
SPI1.misoPinConfig.onlyInternalResistor = scripting.forceWrite(false);
SPI1.misoPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
SPI1.peripheral.$assign                 = "SPI1";
SPI1.peripheral.sclkPin.$assign         = "PB9";
SPI1.peripheral.mosiPin.$assign         = "PB8";
SPI1.peripheral.misoPin.$assign         = "PB7";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.validateClkStatus     = true;
SYSCTL.MFPCLKEn              = true;
SYSCTL.clockTreeEn           = true;

TIMER1.$name                       = "TIMER_0";
TIMER1.timerMode                   = "PERIODIC";
TIMER1.timerPeriod                 = "25us";
TIMER1.event1PublisherChannel      = 1;
TIMER1.event1ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.event2ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.event2PublisherChannel      = 2;

UART1.$name                            = "Console";
UART1.targetBaudRate                   = 115200;
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";

ProjectConfig.genLibCMSIS = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
ADC121.DMA_CHANNEL.peripheral.$suggestSolution     = "DMA_CH1";
ADC122.peripheral.adcPin0.$suggestSolution         = "PA15";
ADC122.DMA_CHANNEL.peripheral.$suggestSolution     = "DMA_CH0";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PB10";
GPIO3.associatedPins[0].pin.$suggestSolution       = "PB13";
GPIO3.associatedPins[1].pin.$suggestSolution       = "PB15";
GPIO3.associatedPins[2].pin.$suggestSolution       = "PB16";
GPIO3.associatedPins[3].pin.$suggestSolution       = "PA12";
GPIO4.associatedPins[0].pin.$suggestSolution       = "PB0";
GPIO4.associatedPins[1].pin.$suggestSolution       = "PB1";
DAC121.peripheral.dacPin.$suggestSolution          = "PA13";
TIMER1.peripheral.$suggestSolution                 = "TIMA0";
UART1.peripheral.$suggestSolution                  = "UART0";
UART1.peripheral.rxPin.$suggestSolution            = "PA1";
UART1.peripheral.txPin.$suggestSolution            = "PA0";
