******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Thu Jul 31 12:39:59 2025

OUTPUT FILE NAME:   <2.8_lcd_adc_NOLVGL_DDS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0001d9a9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0001e0f0  00001f10  R  X
  SRAM                  20200000   00008000  00006370  00001c90  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000190b8   000190b8    r--
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00018ff8   00018ff8    r-- .rodata
000190c0    000190c0    00005038   00005038    r-x
  000190c0    000190c0    00005000   00005000    r-x .text
  0001e0c0    0001e0c0    00000038   00000038    r-- .cinit
20200000    20200000    00006170   00000000    rw-
  20200000    20200000    00005154   00000000    rw- .bss
  20205154    20205154    0000101c   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000190c0    00005000     
                  000190c0    000008cc     arm_cortexM0l_math.a : arm_cfft_radix8_f32.o (.text.arm_radix8_butterfly_f32)
                  0001998c    0000076e                          : arm_cfft_f32.o (.text.arm_cfft_radix8by4_f32)
                  0001a0fa    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0001a0fc    00000724     empty.o (.text.main)
                  0001a820    000002b0     arm_cortexM0l_math.a : arm_cfft_f32.o (.text.arm_cfft_radix8by2_f32)
                  0001aad0    00000260     lcd_init.o (.text.LCD_Init)
                  0001ad30    00000214     lcd.o (.text.LCD_ShowChar)
                  0001af44    000001d8     lcd.o (.text.LCD_ShowChinese12x12)
                  0001b11c    000001d8     lcd.o (.text.LCD_ShowChinese16x16)
                  0001b2f4    000001d8     lcd.o (.text.LCD_ShowChinese24x24)
                  0001b4cc    000001d8     lcd.o (.text.LCD_ShowChinese32x32)
                  0001b6a4    000001c0     arm_cortexM0l_math.a : arm_cfft_f32.o (.text.arm_cfft_f32)
                  0001b864    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0001b9f6    00000168     arm_cortexM0l_math.a : arm_cmplx_mag_f32.o (.text.arm_cmplx_mag_f32)
                  0001bb5e    00000002     --HOLE-- [fill = 0]
                  0001bb60    00000130     adc_channel.o (.text.adc_channel_process_data)
                  0001bc90    0000011c     lcd.o (.text.LCD_ShowFloatNum1)
                  0001bdac    00000116     lcd.o (.text.LCD_ShowChinese)
                  0001bec2    00000112     arm_cortexM0l_math.a : arm_bitreversal2.o (.text.arm_bitreversal_32)
                  0001bfd4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0001c0e0    00000108     ad9850.o (.text.dds_set)
                  0001c1e8    000000ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0001c2d4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0001c3bc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0001c4a0    000000d8                            : addsf3.S.obj (.text)
                  0001c578    000000d2     arm_cortexM0l_math.a : arm_rms_f32.o (.text.arm_rms_f32)
                  0001c64a    000000b4                          : arm_offset_f32.o (.text.arm_offset_f32)
                  0001c6fe    00000002     --HOLE-- [fill = 0]
                  0001c700    000000b0                          : arm_cfft_init_f32.o (.text.arm_cfft_init_f32)
                  0001c7b0    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  0001c850    000000a0     libc.a : e_sqrtf.c.obj (.text.sqrtf)
                  0001c8f0    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  0001c98c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0001ca18    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0001caa4    00000088     arm_cortexM0l_math.a : arm_mean_f32.o (.text.arm_mean_f32)
                  0001cb2c    00000084     lcd.o (.text.LCD_Fill)
                  0001cbb0    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0001cc34    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0001ccb6    0000007e     adc_channel.o (.text.adc_channel_display_values)
                  0001cd34    0000007c     adc_channel.o (.text.adc_channel_init)
                  0001cdb0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0001ce2c    0000007a     lcd.o (.text.LCD_ShowString)
                  0001cea6    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0001ceb0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  0001cf24    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  0001cf88    00000064     lcd_init.o (.text.LCD_Writ_Bus)
                  0001cfec    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0001d04e    00000002     --HOLE-- [fill = 0]
                  0001d050    00000060     empty.o (.text.GROUP1_IRQHandler)
                  0001d0b0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0001d108    00000052     lcd_init.o (.text.LCD_Address_Set)
                  0001d15a    00000002     --HOLE-- [fill = 0]
                  0001d15c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  0001d1a8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0001d1f4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0001d240    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0001d28a    00000002     --HOLE-- [fill = 0]
                  0001d28c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0001d2d4    00000044                 : dl_spi.o (.text.DL_SPI_init)
                  0001d318    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0001d35c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0001d39e    00000002     --HOLE-- [fill = 0]
                  0001d3a0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  0001d3e0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_Console_init)
                  0001d420    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFTspi_init)
                  0001d460    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0001d4a0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0001d4e0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0001d51c    0000003c     ad9850.o (.text.dds_databitwrite)
                  0001d558    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0001d594    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0001d5ce    00000002     --HOLE-- [fill = 0]
                  0001d5d0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0001d60a    00000002     --HOLE-- [fill = 0]
                  0001d60c    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  0001d644    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0001d67c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0001d6b0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0001d6e4    00000034     ad9850.o (.text.dds_reset)
                  0001d718    00000030     empty.o (.text.DL_DMA_setTransferSize)
                  0001d748    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  0001d778    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  0001d7a8    00000030     lcd_init.o (.text.LCD_WR_REG)
                  0001d7d8    00000030     lcd.o (.text.mypow)
                  0001d808    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  0001d834    0000002c     lcd.o (.text.LCD_DrawPoint)
                  0001d860    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  0001d88c    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  0001d8b8    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0001d8e0    00000028     empty.o (.text.DL_DMA_setDestAddr)
                  0001d908    00000028     empty.o (.text.DL_DMA_setSrcAddr)
                  0001d930    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  0001d958    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  0001d980    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0001d9a8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0001d9d0    00000026     empty.o (.text.DL_DMA_enableChannel)
                  0001d9f6    00000002     --HOLE-- [fill = 0]
                  0001d9f8    00000024     empty.o (.text.DL_ADC12_getMemResultAddress)
                  0001da1c    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  0001da40    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0001da64    00000020     empty.o (.text.ADC0_IRQHandler)
                  0001da84    00000020     empty.o (.text.ADC1_IRQHandler)
                  0001daa4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0001dac4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0001dae2    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0001db00    0000001e     lcd_init.o (.text.LCD_WR_DATA)
                  0001db1e    00000002     --HOLE-- [fill = 0]
                  0001db20    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  0001db3c    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  0001db58    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  0001db74    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  0001db90    0000001c     empty.o (.text.DL_GPIO_clearInterruptStatus)
                  0001dbac    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0001dbc8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0001dbe4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0001dc00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0001dc1c    0000001c     empty.o (.text.DL_Interrupt_getPendingGroup)
                  0001dc38    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0001dc54    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0001dc70    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  0001dc8a    00000002     --HOLE-- [fill = 0]
                  0001dc8c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  0001dca4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0001dcbc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  0001dcd4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0001dcec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0001dd04    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0001dd1c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  0001dd34    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0001dd4c    00000018     ad9850.o (.text.DL_GPIO_setPins)
                  0001dd64    00000018     lcd_init.o (.text.DL_GPIO_setPins)
                  0001dd7c    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  0001dd94    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  0001ddac    00000018     lcd_init.o (.text.DL_SPI_isBusy)
                  0001ddc4    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  0001dddc    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0001ddf4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0001de0c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0001de24    00000018     empty.o (.text.DL_Timer_startCounter)
                  0001de3c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0001de54    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0001de6c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  0001de84    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0001de9a    00000016     lcd_init.o (.text.DL_SPI_transmitData8)
                  0001deb0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0001dec6    00000016     board.o (.text.delay_ms)
                  0001dedc    00000014     ad9850.o (.text.DL_GPIO_clearPins)
                  0001def0    00000014     lcd_init.o (.text.DL_GPIO_clearPins)
                  0001df04    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0001df18    00000014     lcd_init.o (.text.DL_SPI_receiveData8)
                  0001df2c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0001df40    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0001df54    00000014     lcd_init.o (.text.LCD_WR_DATA8)
                  0001df68    00000014     ad9850.o (.text.dds_clkclr)
                  0001df7c    00000014     ad9850.o (.text.dds_clkset)
                  0001df90    00000014     ad9850.o (.text.dds_datclr)
                  0001dfa4    00000014     ad9850.o (.text.dds_datset)
                  0001dfb8    00000012     empty.o (.text.DL_ADC12_getPendingInterrupt)
                  0001dfca    00000012     empty.o (.text.DL_GPIO_getPendingInterrupt)
                  0001dfdc    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0001dfee    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  0001e000    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0001e012    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0001e024    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0001e034    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  0001e044    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0001e052    00000002     --HOLE-- [fill = 0]
                  0001e054    0000000c     ti_msp_dl_config.o (.text.DL_SYSCTL_getClockStatus)
                  0001e060    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0001e06c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0001e078    0000000a     ad9850.o (.text.dds_clkdelay)
                  0001e082    00000002     --HOLE-- [fill = 0]
                  0001e084    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0001e08c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0001e094    00000008     ad9850.o (.text.dds_get_ref_clock)
                  0001e09c    00000006     libc.a : exit.c.obj (.text:abort)
                  0001e0a2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0001e0a6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0001e0aa    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0001e0ae    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0001e0b2    00000004     ad9850.o (.text.dds_verify_config)
                  0001e0b6    0000000a     --HOLE-- [fill = 0]

.cinit     0    0001e0c0    00000038     
                  0001e0c0    0000000d     (.cinit..data.load) [load image, compression = lzss]
                  0001e0cd    00000003     --HOLE-- [fill = 0]
                  0001e0d0    0000000c     (__TI_handler_table)
                  0001e0dc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0001e0e4    00000010     (__TI_cinit_table)
                  0001e0f4    00000004     --HOLE-- [fill = 0]

.rodata    0    000000c0    00018ff8     
                  000000c0    00008000     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_4096)
                  000080c0    00004000                          : arm_common_tables.o (.rodata.twiddleCoef_2048)
                  0000c0c0    00002000                          : arm_common_tables.o (.rodata.twiddleCoef_1024)
                  0000e0c0    00001f80                          : arm_common_tables.o (.rodata.armBitRevIndexTable4096)
                  00010040    00001dc0                          : arm_common_tables.o (.rodata.armBitRevIndexTable2048)
                  00011e00    000017c0     lcd.o (.rodata.ascii_3216)
                  000135c0    000011d0     lcd.o (.rodata.ascii_2412)
                  00014790    00001000     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_512)
                  00015790    00000e10                          : arm_common_tables.o (.rodata.armBitRevIndexTable1024)
                  000165a0    00000800                          : arm_common_tables.o (.rodata.twiddleCoef_256)
                  00016da0    000005f0     lcd.o (.rodata.ascii_1608)
                  00017390    00000474     lcd.o (.rodata.ascii_1206)
                  00017804    00000400     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_128)
                  00017c04    00000380                          : arm_common_tables.o (.rodata.armBitRevIndexTable512)
                  00017f84    00000370                          : arm_common_tables.o (.rodata.armBitRevIndexTable256)
                  000182f4    0000028a     lcd.o (.rodata.tfont32)
                  0001857e    00000002     ti_msp_dl_config.o (.rodata.gConsoleClockConfig)
                  00018580    00000200     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_64)
                  00018780    000001a0                          : arm_common_tables.o (.rodata.armBitRevIndexTable128)
                  00018920    00000198     lcd.o (.rodata.tfont16)
                  00018ab8    00000172     lcd.o (.rodata.tfont24)
                  00018c2a    00000002     ti_msp_dl_config.o (.rodata.gTFTspi_clockConfig)
                  00018c2c    00000100     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_32)
                  00018d2c    000000bd     lcd.o (.rodata.tfont12)
                  00018de9    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00018dec    00000080     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_16)
                  00018e6c    00000070                          : arm_common_tables.o (.rodata.armBitRevIndexTable64)
                  00018edc    00000060                          : arm_common_tables.o (.rodata.armBitRevIndexTable32)
                  00018f3c    00000028                          : arm_common_tables.o (.rodata.armBitRevIndexTable16)
                  00018f64    00000019     empty.o (.rodata.str1.7401042497206923953.1)
                  00018f7d    00000002     empty.o (.rodata.str1.150872071346279890.1)
                  00018f7f    00000001     --HOLE-- [fill = 0]
                  00018f80    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00018f98    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00018fb0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00018fc4    00000010     arm_cortexM0l_math.a : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len1024)
                  00018fd4    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len128)
                  00018fe4    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len16)
                  00018ff4    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len2048)
                  00019004    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len256)
                  00019014    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len32)
                  00019024    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len4096)
                  00019034    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len512)
                  00019044    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len64)
                  00019054    00000010     empty.o (.rodata.str1.9517790425240694019.1)
                  00019064    0000000a     ti_msp_dl_config.o (.rodata.gConsoleConfig)
                  0001906e    0000000a     ti_msp_dl_config.o (.rodata.gTFTspi_config)
                  00019078    00000009     empty.o (.rodata.str1.17669528882079347314.1)
                  00019081    00000009     empty.o (.rodata.str1.2196762768037919588.1)
                  0001908a    00000002     empty.o (.rodata.str1.16704889451495720520.1)
                  0001908c    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00019094    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  0001909c    00000008     empty.o (.rodata.str1.14685083708502177989.1)
                  000190a4    00000008     empty.o (.rodata.str1.254342170260855183.1)
                  000190ac    00000005     empty.o (.rodata.str1.11898133897667081452.1)
                  000190b1    00000004     empty.o (.rodata.str1.15706828512682300538.1)
                  000190b5    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00005154     UNINITIALIZED
                  20200000    00002000     empty.o (.bss.g_fft_input)
                  20202000    00001000     empty.o (.bss.g_fft_output)
                  20203000    00001000     empty.o (.bss.g_float_samples_current)
                  20204000    00001000     empty.o (.bss.g_float_samples_voltage)
                  20205000    000000bc     (.common:gTIMER_0Backup)
                  202050bc    00000060     empty.o (.bss.g_channels)
                  2020511c    00000028     (.common:gTFTspiBackup)
                  20205144    00000010     empty.o (.bss.g_fft_instance)

.data      0    20205154    0000101c     UNINITIALIZED
                  20205154    00000800     empty.o (.data.gADCSamples)
                  20205954    00000800     empty.o (.data.gADCSamples_ch1)
                  20206154    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20206158    00000004     empty.o (.data.adc0_done)
                  2020615c    00000004     empty.o (.data.adc1_done)
                  20206160    00000004     empty.o (.data.g_button_flags)
                  20206164    00000004     empty.o (.data.g_current_freq_hz)
                  20206168    00000004     empty.o (.data.main.fft_counter)
                  2020616c    00000004     empty.o (.data.main.last_freq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       empty.o                        2350    88        24712  
       ti_msp_dl_config.o             2756    111       228    
       adc_channel.o                  554     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5668    391       24940  
                                                               
    .\BSP\AD9850\
       ad9850.o                       522     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         522     0         0      
                                                               
    .\BSP\LCD\
       lcd.o                          3328    14917     0      
       lcd_init.o                     998     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4326    14917     0      
                                                               
    .\Board\
       board.o                        22      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         22      0         0      
                                                               
    G:/ti/SDK/mspm0-sdk-main/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a
       arm_common_tables.o            0       87128     0      
       arm_cfft_f32.o                 3038    0         0      
       arm_cfft_radix8_f32.o          2252    0         0      
       arm_cmplx_mag_f32.o            360     0         0      
       arm_bitreversal2.o             274     0         0      
       arm_rms_f32.o                  210     0         0      
       arm_offset_f32.o               180     0         0      
       arm_cfft_init_f32.o            176     0         0      
       arm_const_structs.o            0       144       0      
       arm_mean_f32.o                 136     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         6626    87272     0      
                                                               
    G:/ti/SDK/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   100     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         686     0         0      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_sqrtf.c.obj                  160     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         468     0         4      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       aeabi_memset.S.obj             12      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2122    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       49        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   20444   102629    25456  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0001e0e4 records: 2, size/record: 8, table size: 16
	.data: load addr=0001e0c0, load size=0000000d bytes, run addr=20205154, run size=0000101c bytes, compression=lzss
	.bss: load addr=0001e0dc, load size=00000008 bytes, run addr=20200000, run size=00005154 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0001e0d0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                              
-------   ----                              
0001da65  ADC0_IRQHandler                   
0001da85  ADC1_IRQHandler                   
0001e0a3  AES_IRQHandler                    
0001e0a6  C$$EXIT                           
0001e0a3  CANFD0_IRQHandler                 
0001e0a3  DAC0_IRQHandler                   
0001d3a1  DL_ADC12_setClockConfig           
0001cea7  DL_Common_delayCycles             
0001d1a9  DL_DMA_initChannel                
0001d2d5  DL_SPI_init                       
0001dfdd  DL_SPI_setClockConfig             
0001cf25  DL_SYSCTL_setHFCLKSourceHFXTParams
0001c2d5  DL_Timer_initTimerMode            
0001dc55  DL_Timer_setClockConfig           
0001d28d  DL_UART_init                      
0001dfef  DL_UART_setClockConfig            
0001e0a3  DMA_IRQHandler                    
0001e0a3  Default_Handler                   
0001e0a3  GROUP0_IRQHandler                 
0001d051  GROUP1_IRQHandler                 
0001e0a7  HOSTexit                          
0001e0a3  HardFault_Handler                 
0001e0a3  I2C0_IRQHandler                   
0001e0a3  I2C1_IRQHandler                   
0001d109  LCD_Address_Set                   
0001d835  LCD_DrawPoint                     
0001cb2d  LCD_Fill                          
0001aad1  LCD_Init                          
0001ad31  LCD_ShowChar                      
0001bdad  LCD_ShowChinese                   
0001af45  LCD_ShowChinese12x12              
0001b11d  LCD_ShowChinese16x16              
0001b2f5  LCD_ShowChinese24x24              
0001b4cd  LCD_ShowChinese32x32              
0001bc91  LCD_ShowFloatNum1                 
0001ce2d  LCD_ShowString                    
0001db01  LCD_WR_DATA                       
0001df55  LCD_WR_DATA8                      
0001d7a9  LCD_WR_REG                        
0001cf89  LCD_Writ_Bus                      
0001e0a3  NMI_Handler                       
0001e0a3  PendSV_Handler                    
0001e0a3  RTC_IRQHandler                    
0001e0ab  Reset_Handler                     
0001e0a3  SPI0_IRQHandler                   
0001e0a3  SPI1_IRQHandler                   
0001e0a3  SVC_Handler                       
0001c8f1  SYSCFG_DL_ADC12_0_init            
0001c7b1  SYSCFG_DL_ADC12_1_init            
0001d3e1  SYSCFG_DL_Console_init            
0001d959  SYSCFG_DL_DMA_CH0_init            
0001d861  SYSCFG_DL_DMA_CH1_init            
0001e061  SYSCFG_DL_DMA_init                
0001c1e9  SYSCFG_DL_GPIO_init               
0001de6d  SYSCFG_DL_SYSCTL_CLK_init         
0001d6b1  SYSCFG_DL_SYSCTL_init             
0001d421  SYSCFG_DL_TFTspi_init             
0001d0b1  SYSCFG_DL_TIMER_0_init            
0001d319  SYSCFG_DL_init                    
0001c98d  SYSCFG_DL_initPower               
0001e0a3  SysTick_Handler                   
0001e0a3  TIMA0_IRQHandler                  
0001e0a3  TIMA1_IRQHandler                  
0001e0a3  TIMG0_IRQHandler                  
0001e0a3  TIMG12_IRQHandler                 
0001e0a3  TIMG6_IRQHandler                  
0001e0a3  TIMG7_IRQHandler                  
0001e0a3  TIMG8_IRQHandler                  
0001e001  TI_memcpy_small                   
0001e045  TI_memset_small                   
0001e0a3  UART0_IRQHandler                  
0001e0a3  UART1_IRQHandler                  
0001e0a3  UART2_IRQHandler                  
0001e0a3  UART3_IRQHandler                  
20208000  __STACK_END                       
00000200  __STACK_SIZE                      
00000000  __TI_ATRegion0_region_sz          
00000000  __TI_ATRegion0_src_addr           
00000000  __TI_ATRegion0_trg_addr           
00000000  __TI_ATRegion1_region_sz          
00000000  __TI_ATRegion1_src_addr           
00000000  __TI_ATRegion1_trg_addr           
00000000  __TI_ATRegion2_region_sz          
00000000  __TI_ATRegion2_src_addr           
00000000  __TI_ATRegion2_trg_addr           
0001e0e4  __TI_CINIT_Base                   
0001e0f4  __TI_CINIT_Limit                  
0001e0f4  __TI_CINIT_Warm                   
0001e0d0  __TI_Handler_Table_Base           
0001e0dc  __TI_Handler_Table_Limit          
0001d559  __TI_auto_init_nobinit_nopinit    
0001cdb1  __TI_decompress_lzss              
0001e013  __TI_decompress_none              
ffffffff  __TI_pprof_out_hndl               
ffffffff  __TI_prof_data_size               
ffffffff  __TI_prof_data_start              
00000000  __TI_static_base__                
0001e035  __TI_zero_init                    
0001b86f  __adddf3                          
0001c4ab  __addsf3                          
0001ceb1  __aeabi_d2f                       
0001d35d  __aeabi_d2uiz                     
0001b86f  __aeabi_dadd                      
0001bfd5  __aeabi_ddiv                      
0001c3bd  __aeabi_dmul                      
0001b865  __aeabi_dsub                      
20206154  __aeabi_errno                     
0001e085  __aeabi_errno_addr                
0001d4a1  __aeabi_f2d                       
0001d645  __aeabi_f2iz                      
0001c4ab  __aeabi_fadd                      
0001cfed  __aeabi_fcmpeq                    
0001d029  __aeabi_fcmpge                    
0001d03d  __aeabi_fcmpgt                    
0001d015  __aeabi_fcmple                    
0001d001  __aeabi_fcmplt                    
0001cc35  __aeabi_fdiv                      
0001ca19  __aeabi_fmul                      
0001c4a1  __aeabi_fsub                      
0001a0fb  __aeabi_idiv0                     
0001e06d  __aeabi_memclr                    
0001e06d  __aeabi_memclr4                   
0001e06d  __aeabi_memclr8                   
0001e08d  __aeabi_memcpy                    
0001e08d  __aeabi_memcpy4                   
0001e08d  __aeabi_memcpy8                   
0001da41  __aeabi_ui2d                      
0001d981  __aeabi_ui2f                      
0001d461  __aeabi_uidiv                     
0001d461  __aeabi_uidivmod                  
ffffffff  __binit__                         
0001d595  __cmpsf2                          
0001bfd5  __divdf3                          
0001cc35  __divsf3                          
0001d595  __eqsf2                           
0001d4a1  __extendsfdf2                     
0001d645  __fixsfsi                         
0001d35d  __fixunsdfsi                      
0001da41  __floatunsidf                     
0001d981  __floatunsisf                     
0001d4e1  __gesf2                           
0001d4e1  __gtsf2                           
0001d595  __lesf2                           
0001d595  __ltsf2                           
UNDEFED   __mpu_init                        
0001c3bd  __muldf3                          
0001d5d1  __muldsi3                         
0001ca19  __mulsf3                          
0001d595  __nesf2                           
20207e00  __stack                           
20200000  __start___llvm_prf_bits           
20200000  __start___llvm_prf_cnts           
20200000  __stop___llvm_prf_bits            
20200000  __stop___llvm_prf_cnts            
0001b865  __subdf3                          
0001c4a1  __subsf3                          
0001ceb1  __truncdfsf2                      
0001d9a9  _c_int00_noargs                   
UNDEFED   _system_post_cinit                
0001e0af  _system_pre_init                  
0001e09d  abort                             
20206158  adc0_done                         
2020615c  adc1_done                         
0001ccb7  adc_channel_display_values        
0001cd35  adc_channel_init                  
0001bb61  adc_channel_process_data          
00015790  armBitRevIndexTable1024           
00018780  armBitRevIndexTable128            
00018f3c  armBitRevIndexTable16             
00010040  armBitRevIndexTable2048           
00017f84  armBitRevIndexTable256            
00018edc  armBitRevIndexTable32             
0000e0c0  armBitRevIndexTable4096           
00017c04  armBitRevIndexTable512            
00018e6c  armBitRevIndexTable64             
0001bec3  arm_bitreversal_32                
0001b6a5  arm_cfft_f32                      
0001c701  arm_cfft_init_f32                 
0001a821  arm_cfft_radix8by2_f32            
0001998d  arm_cfft_radix8by4_f32            
00018fc4  arm_cfft_sR_f32_len1024           
00018fd4  arm_cfft_sR_f32_len128            
00018fe4  arm_cfft_sR_f32_len16             
00018ff4  arm_cfft_sR_f32_len2048           
00019004  arm_cfft_sR_f32_len256            
00019014  arm_cfft_sR_f32_len32             
00019024  arm_cfft_sR_f32_len4096           
00019034  arm_cfft_sR_f32_len512            
00019044  arm_cfft_sR_f32_len64             
0001b9f7  arm_cmplx_mag_f32                 
0001caa5  arm_mean_f32                      
0001c64b  arm_offset_f32                    
000190c1  arm_radix8_butterfly_f32          
0001c579  arm_rms_f32                       
00017390  ascii_1206                        
00016da0  ascii_1608                        
000135c0  ascii_2412                        
00011e00  ascii_3216                        
ffffffff  binit                             
0001e095  dds_get_ref_clock                 
0001d6e5  dds_reset                         
0001c0e1  dds_set                           
0001e0b3  dds_verify_config                 
0001dec7  delay_ms                          
20205154  gADCSamples                       
20205954  gADCSamples_ch1                   
2020511c  gTFTspiBackup                     
20205000  gTIMER_0Backup                    
20206160  g_button_flags                    
00000000  interruptVectors                  
0001a0fd  main                              
0001d7d9  mypow                             
0001c851  sqrtf                             
00018d2c  tfont12                           
00018920  tfont16                           
00018ab8  tfont24                           
000182f4  tfont32                           
0000c0c0  twiddleCoef_1024                  
00017804  twiddleCoef_128                   
00018dec  twiddleCoef_16                    
000080c0  twiddleCoef_2048                  
000165a0  twiddleCoef_256                   
00018c2c  twiddleCoef_32                    
000000c0  twiddleCoef_4096                  
00014790  twiddleCoef_512                   
00018580  twiddleCoef_64                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                              
-------   ----                              
00000000  __TI_ATRegion0_region_sz          
00000000  __TI_ATRegion0_src_addr           
00000000  __TI_ATRegion0_trg_addr           
00000000  __TI_ATRegion1_region_sz          
00000000  __TI_ATRegion1_src_addr           
00000000  __TI_ATRegion1_trg_addr           
00000000  __TI_ATRegion2_region_sz          
00000000  __TI_ATRegion2_src_addr           
00000000  __TI_ATRegion2_trg_addr           
00000000  __TI_static_base__                
00000000  interruptVectors                  
000000c0  twiddleCoef_4096                  
00000200  __STACK_SIZE                      
000080c0  twiddleCoef_2048                  
0000c0c0  twiddleCoef_1024                  
0000e0c0  armBitRevIndexTable4096           
00010040  armBitRevIndexTable2048           
00011e00  ascii_3216                        
000135c0  ascii_2412                        
00014790  twiddleCoef_512                   
00015790  armBitRevIndexTable1024           
000165a0  twiddleCoef_256                   
00016da0  ascii_1608                        
00017390  ascii_1206                        
00017804  twiddleCoef_128                   
00017c04  armBitRevIndexTable512            
00017f84  armBitRevIndexTable256            
000182f4  tfont32                           
00018580  twiddleCoef_64                    
00018780  armBitRevIndexTable128            
00018920  tfont16                           
00018ab8  tfont24                           
00018c2c  twiddleCoef_32                    
00018d2c  tfont12                           
00018dec  twiddleCoef_16                    
00018e6c  armBitRevIndexTable64             
00018edc  armBitRevIndexTable32             
00018f3c  armBitRevIndexTable16             
00018fc4  arm_cfft_sR_f32_len1024           
00018fd4  arm_cfft_sR_f32_len128            
00018fe4  arm_cfft_sR_f32_len16             
00018ff4  arm_cfft_sR_f32_len2048           
00019004  arm_cfft_sR_f32_len256            
00019014  arm_cfft_sR_f32_len32             
00019024  arm_cfft_sR_f32_len4096           
00019034  arm_cfft_sR_f32_len512            
00019044  arm_cfft_sR_f32_len64             
000190c1  arm_radix8_butterfly_f32          
0001998d  arm_cfft_radix8by4_f32            
0001a0fb  __aeabi_idiv0                     
0001a0fd  main                              
0001a821  arm_cfft_radix8by2_f32            
0001aad1  LCD_Init                          
0001ad31  LCD_ShowChar                      
0001af45  LCD_ShowChinese12x12              
0001b11d  LCD_ShowChinese16x16              
0001b2f5  LCD_ShowChinese24x24              
0001b4cd  LCD_ShowChinese32x32              
0001b6a5  arm_cfft_f32                      
0001b865  __aeabi_dsub                      
0001b865  __subdf3                          
0001b86f  __adddf3                          
0001b86f  __aeabi_dadd                      
0001b9f7  arm_cmplx_mag_f32                 
0001bb61  adc_channel_process_data          
0001bc91  LCD_ShowFloatNum1                 
0001bdad  LCD_ShowChinese                   
0001bec3  arm_bitreversal_32                
0001bfd5  __aeabi_ddiv                      
0001bfd5  __divdf3                          
0001c0e1  dds_set                           
0001c1e9  SYSCFG_DL_GPIO_init               
0001c2d5  DL_Timer_initTimerMode            
0001c3bd  __aeabi_dmul                      
0001c3bd  __muldf3                          
0001c4a1  __aeabi_fsub                      
0001c4a1  __subsf3                          
0001c4ab  __addsf3                          
0001c4ab  __aeabi_fadd                      
0001c579  arm_rms_f32                       
0001c64b  arm_offset_f32                    
0001c701  arm_cfft_init_f32                 
0001c7b1  SYSCFG_DL_ADC12_1_init            
0001c851  sqrtf                             
0001c8f1  SYSCFG_DL_ADC12_0_init            
0001c98d  SYSCFG_DL_initPower               
0001ca19  __aeabi_fmul                      
0001ca19  __mulsf3                          
0001caa5  arm_mean_f32                      
0001cb2d  LCD_Fill                          
0001cc35  __aeabi_fdiv                      
0001cc35  __divsf3                          
0001ccb7  adc_channel_display_values        
0001cd35  adc_channel_init                  
0001cdb1  __TI_decompress_lzss              
0001ce2d  LCD_ShowString                    
0001cea7  DL_Common_delayCycles             
0001ceb1  __aeabi_d2f                       
0001ceb1  __truncdfsf2                      
0001cf25  DL_SYSCTL_setHFCLKSourceHFXTParams
0001cf89  LCD_Writ_Bus                      
0001cfed  __aeabi_fcmpeq                    
0001d001  __aeabi_fcmplt                    
0001d015  __aeabi_fcmple                    
0001d029  __aeabi_fcmpge                    
0001d03d  __aeabi_fcmpgt                    
0001d051  GROUP1_IRQHandler                 
0001d0b1  SYSCFG_DL_TIMER_0_init            
0001d109  LCD_Address_Set                   
0001d1a9  DL_DMA_initChannel                
0001d28d  DL_UART_init                      
0001d2d5  DL_SPI_init                       
0001d319  SYSCFG_DL_init                    
0001d35d  __aeabi_d2uiz                     
0001d35d  __fixunsdfsi                      
0001d3a1  DL_ADC12_setClockConfig           
0001d3e1  SYSCFG_DL_Console_init            
0001d421  SYSCFG_DL_TFTspi_init             
0001d461  __aeabi_uidiv                     
0001d461  __aeabi_uidivmod                  
0001d4a1  __aeabi_f2d                       
0001d4a1  __extendsfdf2                     
0001d4e1  __gesf2                           
0001d4e1  __gtsf2                           
0001d559  __TI_auto_init_nobinit_nopinit    
0001d595  __cmpsf2                          
0001d595  __eqsf2                           
0001d595  __lesf2                           
0001d595  __ltsf2                           
0001d595  __nesf2                           
0001d5d1  __muldsi3                         
0001d645  __aeabi_f2iz                      
0001d645  __fixsfsi                         
0001d6b1  SYSCFG_DL_SYSCTL_init             
0001d6e5  dds_reset                         
0001d7a9  LCD_WR_REG                        
0001d7d9  mypow                             
0001d835  LCD_DrawPoint                     
0001d861  SYSCFG_DL_DMA_CH1_init            
0001d959  SYSCFG_DL_DMA_CH0_init            
0001d981  __aeabi_ui2f                      
0001d981  __floatunsisf                     
0001d9a9  _c_int00_noargs                   
0001da41  __aeabi_ui2d                      
0001da41  __floatunsidf                     
0001da65  ADC0_IRQHandler                   
0001da85  ADC1_IRQHandler                   
0001db01  LCD_WR_DATA                       
0001dc55  DL_Timer_setClockConfig           
0001de6d  SYSCFG_DL_SYSCTL_CLK_init         
0001dec7  delay_ms                          
0001df55  LCD_WR_DATA8                      
0001dfdd  DL_SPI_setClockConfig             
0001dfef  DL_UART_setClockConfig            
0001e001  TI_memcpy_small                   
0001e013  __TI_decompress_none              
0001e035  __TI_zero_init                    
0001e045  TI_memset_small                   
0001e061  SYSCFG_DL_DMA_init                
0001e06d  __aeabi_memclr                    
0001e06d  __aeabi_memclr4                   
0001e06d  __aeabi_memclr8                   
0001e085  __aeabi_errno_addr                
0001e08d  __aeabi_memcpy                    
0001e08d  __aeabi_memcpy4                   
0001e08d  __aeabi_memcpy8                   
0001e095  dds_get_ref_clock                 
0001e09d  abort                             
0001e0a3  AES_IRQHandler                    
0001e0a3  CANFD0_IRQHandler                 
0001e0a3  DAC0_IRQHandler                   
0001e0a3  DMA_IRQHandler                    
0001e0a3  Default_Handler                   
0001e0a3  GROUP0_IRQHandler                 
0001e0a3  HardFault_Handler                 
0001e0a3  I2C0_IRQHandler                   
0001e0a3  I2C1_IRQHandler                   
0001e0a3  NMI_Handler                       
0001e0a3  PendSV_Handler                    
0001e0a3  RTC_IRQHandler                    
0001e0a3  SPI0_IRQHandler                   
0001e0a3  SPI1_IRQHandler                   
0001e0a3  SVC_Handler                       
0001e0a3  SysTick_Handler                   
0001e0a3  TIMA0_IRQHandler                  
0001e0a3  TIMA1_IRQHandler                  
0001e0a3  TIMG0_IRQHandler                  
0001e0a3  TIMG12_IRQHandler                 
0001e0a3  TIMG6_IRQHandler                  
0001e0a3  TIMG7_IRQHandler                  
0001e0a3  TIMG8_IRQHandler                  
0001e0a3  UART0_IRQHandler                  
0001e0a3  UART1_IRQHandler                  
0001e0a3  UART2_IRQHandler                  
0001e0a3  UART3_IRQHandler                  
0001e0a6  C$$EXIT                           
0001e0a7  HOSTexit                          
0001e0ab  Reset_Handler                     
0001e0af  _system_pre_init                  
0001e0b3  dds_verify_config                 
0001e0d0  __TI_Handler_Table_Base           
0001e0dc  __TI_Handler_Table_Limit          
0001e0e4  __TI_CINIT_Base                   
0001e0f4  __TI_CINIT_Limit                  
0001e0f4  __TI_CINIT_Warm                   
20200000  __start___llvm_prf_bits           
20200000  __start___llvm_prf_cnts           
20200000  __stop___llvm_prf_bits            
20200000  __stop___llvm_prf_cnts            
20205000  gTIMER_0Backup                    
2020511c  gTFTspiBackup                     
20205154  gADCSamples                       
20205954  gADCSamples_ch1                   
20206154  __aeabi_errno                     
20206158  adc0_done                         
2020615c  adc1_done                         
20206160  g_button_flags                    
20207e00  __stack                           
20208000  __STACK_END                       
ffffffff  __TI_pprof_out_hndl               
ffffffff  __TI_prof_data_size               
ffffffff  __TI_prof_data_start              
ffffffff  __binit__                         
ffffffff  binit                             
UNDEFED   __mpu_init                        
UNDEFED   _system_post_cinit                

[226 symbols]
