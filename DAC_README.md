# DAC模块配置说明

## 功能概述
本项目已成功添加DAC12模块，可以输出固定电压。

## 硬件配置
- **DAC输出引脚**: PA13
- **参考电压**: VEREFP/VEREFN (通常为3.3V/0V)
- **分辨率**: 12位 (0-4095)
- **输出范围**: 0V - 3.3V

## 软件功能
1. **固定电压输出**: 初始输出约1.65V (中间电压)
2. **按键控制**: 
   - 系统每5秒自动切换控制模式
   - DDS模式: 按键控制DDS频率
   - DAC模式: 按键控制DAC输出电压
3. **实时显示**: LCD显示当前DAC输出电压值

## 控制方式
- **UP按键**: 增加电压 (每次约0.08V)
- **DOWN按键**: 减少电压 (每次约0.08V)
- **模式指示**: 
  - "DDS" (蓝色) = 控制DDS频率
  - "DAC" (红色) = 控制DAC电压

## 测试方法
1. 编译并下载程序到MSPM0G3507
2. 连接万用表到PA13引脚
3. 观察LCD显示的电压值
4. 等待模式切换到DAC模式
5. 按UP/DOWN按键调节电压
6. 用万用表验证实际输出电压

## 下一步计划
- [ ] 输出正弦波
- [ ] 可调频率正弦波
- [ ] 波形选择功能
