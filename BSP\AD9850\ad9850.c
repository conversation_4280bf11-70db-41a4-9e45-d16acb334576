#include "ad9850.h"
#include "ti_msp_dl_config.h"

// Define GPIOs matching the current SysConfig settings for GPIOB
#define IOA_W_CLK_PORT       GPIOB
#define IOA_W_CLK_PIN        DL_GPIO_PIN_13
#define IOA_DDS_DATA_PORT    GPIOB
#define IOA_DDS_DATA_PIN     DL_GPIO_PIN_15
#define IOA_FQ_UD_PORT       GPIOB
#define IOA_FQ_UD_PIN        DL_GPIO_PIN_16
#define IOA_DDS_RESET_PORT   GPIOA
#define IOA_DDS_RESET_PIN    DL_GPIO_PIN_12

// AD9850 参考时钟配置
// 根据syscfg配置，外部晶振为40MHz，AD9850内部6倍频 = 240MHz
// 但标准AD9850通常使用125MHz参考时钟，需要根据实际硬件调整
#define AD9850_REF_CLOCK_HZ  125000000UL  // 125MHz 标准参考时钟
// 如果使用外部40MHz晶振，请改为: #define AD9850_REF_CLOCK_HZ  240000000UL

// DDS control inline functions from the reference code for efficiency
static inline void dds_clkset() { DL_GPIO_setPins(IOA_W_CLK_PORT, IOA_W_CLK_PIN); }
static inline void dds_datset() { DL_GPIO_setPins(IOA_DDS_DATA_PORT, IOA_DDS_DATA_PIN); }
static inline void dds_clkclr() { DL_GPIO_clearPins(IOA_W_CLK_PORT, IOA_W_CLK_PIN); }
static inline void dds_datclr() { DL_GPIO_clearPins(IOA_DDS_DATA_PORT, IOA_DDS_DATA_PIN); }

// Delay between DDS clock signals for timing
static inline void dds_clkdelay() {
    // This is approx 1us delay at 32MHz CPU Clock
    delay_cycles(32); 
}

static inline void dds_databitwrite(uint8_t bit) {
    bit = (bit != 0);
    dds_clkclr();
    if(bit) 
        dds_datset();
    else
        dds_datclr();
    dds_clkdelay();
    dds_clkset();
    dds_clkdelay();
}

void dds_set(uint32_t freq_in_hz) {
    // 恢复原来的计算方法，确保DDS正常工作
    float freq_MHZ = freq_in_hz / 1000000.0;

    // 使用125MHz作为参考时钟（标准配置）
    uint32_t delta_phase = (uint32_t)(freq_MHZ * 4294967296.0 / 125.0 + 0.5);

    // Pull FQ_UD low to prepare for frequency update
    DL_GPIO_clearPins(IOA_FQ_UD_PORT, IOA_FQ_UD_PIN);
    
    // Send 32-bit frequency word (LSB first)
    for(int i = 0; i < 32; i++){
        dds_databitwrite(delta_phase & 1);
        delta_phase >>= 1;
    }
    
    // Send reserved bits
    for(int i = 0; i < 2; i++){
        dds_databitwrite(0);
    }
    
    // Send power-down bit (0 = normal operation)
    for(int i = 0; i < 1; i++){
        dds_databitwrite(0);
    }
    
    // Send 5-bit phase word (unused, all zeros)
    for(int i = 0; i < 5; i++){
        dds_databitwrite(0);
    }
    
    dds_clkclr();
    
    // Pull FQ_UD high to latch the new frequency setting
    DL_GPIO_setPins(IOA_FQ_UD_PORT, IOA_FQ_UD_PIN);
    // Use a long delay matching the reference code (approx. 100us at 32MHz)
    delay_cycles(3200); 
    DL_GPIO_clearPins(IOA_FQ_UD_PORT, IOA_FQ_UD_PIN);
}

void dds_reset(void) {
    DL_GPIO_clearPins(IOA_DDS_RESET_PORT, IOA_DDS_RESET_PIN);
    DL_GPIO_setPins(IOA_DDS_RESET_PORT, IOA_DDS_RESET_PIN);
    // Use a long delay matching the reference code (approx. 1ms at 32MHz)
    delay_cycles(32000);
    DL_GPIO_clearPins(IOA_DDS_RESET_PORT, IOA_DDS_RESET_PIN);
}

// 新增：获取参考时钟频率
uint32_t dds_get_ref_clock(void) {
    return AD9850_REF_CLOCK_HZ;
}

// 新增：验证DDS配置（基础版本）
bool dds_verify_config(void) {
    // 基础验证：检查GPIO配置是否正确
    // 这里可以添加更多的硬件验证逻辑
    return true;  // 暂时返回true，后续可以添加实际验证
}