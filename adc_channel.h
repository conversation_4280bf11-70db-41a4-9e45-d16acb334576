#ifndef ADC_CHANNEL_H_
#define ADC_CHANNEL_H_

#include <stdint.h>
#include <stdbool.h>
#include "wave.h"
#include "arm_math.h"

// ADC通道管理结构
typedef struct {
    // 数据缓冲区
    uint16_t* raw_samples;          // 原始ADC采样数据
    float32_t* float_samples;       // 浮点转换后的数据
    Point* prev_points;             // 上一帧波形坐标点
    
    // 状态标志
    volatile int* done_flag;        // DMA完成标志
    bool has_prev_points;           // 是否有上一帧数据
    
    // 显示参数
    uint16_t color;                 // 波形颜色
    const char* name;               // 通道名称
    const char* unit;               // 单位
    
    // 转换参数
    float conversion_factor;        // ADC到物理量的转换系数
    
    // 计算结果
    float32_t rms_value;           // RMS值
    float32_t mean_value;          // 平均值

    // 性能优化：显示缓存
    float32_t last_displayed_value; // 上次显示的值
    bool value_changed;            // 值是否发生变化
} adc_channel_t;

// 通道管理函数
void adc_channel_init(adc_channel_t* channel, 
                     uint16_t* raw_buffer, 
                     float32_t* float_buffer,
                     Point* point_buffer,
                     volatile int* done_flag,
                     uint16_t color,
                     const char* name,
                     const char* unit,
                     float conversion_factor);

void adc_channel_process_data(adc_channel_t* channel, uint16_t sample_count);
void adc_channel_draw_waveform(adc_channel_t* channel, 
                              uint16_t x0, uint16_t y0, 
                              uint16_t width, uint16_t height,
                              uint16_t data_points,
                              uint16_t bgcolor);
void adc_channel_display_values(adc_channel_t* channel, 
                               uint16_t x, uint16_t y, 
                               uint16_t text_color, uint16_t bg_color);

#endif /* ADC_CHANNEL_H_ */
