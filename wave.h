#ifndef WAVE_H_
#define WAVE_H_

#include "board.h"
#include "lcd.h"

// 定义原点位置常量
#define ORIGIN_TOP_LEFT     0
#define ORIGIN_TOP_RIGHT    1
#define ORIGIN_BOTTOM_LEFT  2
#define ORIGIN_BOTTOM_RIGHT 3

typedef struct {
    u16 x;
    u16 y;
} Point;

// 外部变量声明（定义在wave.c中）
extern Point prevWavePoints[320];  // 最大支持320点
extern u16 prevWaveWidth;
extern u16 prevWaveColor;
extern u16 prevWaveBgColor;


void LCD_DrawGraph(Point* points, u16 count, u16 color);
void LCD_ClearPrevWaveform(u16 bgcolor);
void LCD_ShowWaveform(u16 x0, u16 y0, u16 width, u16 height,
                     uint16_t* data, u16 data_len,
                     u16 color, u16 bgcolor, u8 origin);

#endif /* WAVE_H_ */