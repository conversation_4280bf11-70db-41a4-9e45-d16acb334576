#include "wave.h"

// 全局变量定义
Point prevWavePoints[320];  // 最大支持320点
u16 prevWaveWidth = 0;
u16 prevWaveColor = 0;
u16 prevWaveBgColor = 0;

// 静态函数声明
static u16 transform_x(u16 x, u16 width, u8 origin);
static u16 transform_y(u16 y, u16 height, u8 origin);


/******************************************************************************
      函数说明：根据点绘制函数图像
      入口数据：points  坐标点数组
                count   点的数量
                color   图像的颜色
      返回值：  无
******************************************************************************/


void LCD_DrawGraph(Point* points, u16 count, u16 color)
{
    if (points == NULL || count < 2) {
        return;
    }

    for (u16 i = 0; i < count - 1; i++) {
        LCD_DrawLine(points[i].x, points[i].y, points[i+1].x, points[i+1].y, color);
    }
}
 
/******************************************************************************
      函数说明：清除上一次绘制的波形
      入口数据：bgcolor 背景颜色
      返回值：  无
******************************************************************************/
void LCD_ClearPrevWaveform(u16 bgcolor) {
    if(prevWaveWidth > 0) {
        // 使用背景色重新绘制上一次的波形，相当于擦除
        LCD_DrawGraph(prevWavePoints, prevWaveWidth, bgcolor);
    }
    prevWaveWidth = 0; // 重置状态
}
 
/******************************************************************************
      函数说明：在指定区域内显示ADC波形（带清除功能）
      入口数据：x0, y0    区域起始坐标
                width    区域宽度
                height   区域高度
                data     采样数据数组
                data_len 数据长度
                color    波形颜色
                bgcolor  背景颜色（用于清除上一次波形）
                origin   原点位置
      返回值：  无
******************************************************************************/
void LCD_ShowWaveform(u16 x0, u16 y0, u16 width, u16 height, 
                     uint16_t* data, u16 data_len, 
                     u16 color, u16 bgcolor, u8 origin)
{
    // 清除上一次的波形
    if(prevWaveWidth > 0) {
        LCD_ClearPrevWaveform(prevWaveBgColor);
    }
    
    // 保存当前参数用于下一次清除
    prevWaveBgColor = bgcolor;
    
    // 参数校验
    if(data == NULL || data_len == 0) return;
    if(width == 0 || height == 0) return;
    
    // 限制最大宽度
    if(width > 320) width = 320;
    
    // 计算压缩比例
    float x_ratio = (float)data_len / width;
    float y_ratio = (float)(height - 1) / 4095.0f;  // ADC范围0-4095
    
    // 创建点数组
    Point points[width];
    
    // 遍历所有显示列
    for(u16 x = 0; x < width; x++) {
        // 计算当前列对应的数据索引范围
        u16 start_idx = (u16)(x * x_ratio);
        u16 end_idx = (u16)((x + 1) * x_ratio);
        
        // 确保索引在有效范围内
        if(start_idx >= data_len) start_idx = data_len - 1;
        if(end_idx >= data_len) end_idx = data_len - 1;
        if(start_idx == end_idx && end_idx < data_len - 1) end_idx++;
        
        // 在当前列范围内找平均值
        u32 sum = 0;
        u16 count = 0;
        for(u16 i = start_idx; i <= end_idx; i++) {
            sum += data[i];
            count++;
        }
        u16 avg_val = (count > 0) ? (u16)(sum / count) : 0;
        
        // 计算屏幕Y坐标
        u16 screen_y = (u16)(avg_val * y_ratio);
        if(screen_y >= height) screen_y = height - 1;
        
        // 根据原点位置转换坐标
        u16 screen_x = transform_x(x, width, origin);
        u16 screen_y_trans = transform_y(screen_y, height, origin);
        
        // 保存点坐标
        points[x].x = x0 + screen_x;
        points[x].y = y0 + screen_y_trans;
    }
    
    // 绘制波形
    LCD_DrawGraph(points, width, color);
    
    // 保存当前波形状态用于下一次清除
    prevWaveWidth = width;
    prevWaveColor = color;
    // 保存点数组（只保存宽度范围内的点）
    for(u16 i = 0; i < width; i++) {
        prevWavePoints[i] = points[i];
    }
}
 
/******************************************************************************
      函数说明：根据原点位置转换X坐标
      入口数据：x        区域内的X坐标 (0=左侧，width-1=右侧)
                width    区域宽度
                origin   原点位置
      返回值：  转换后的相对X坐标
******************************************************************************/
static u16 transform_x(u16 x, u16 width, u8 origin) {
    // 原点在左上或左下：保持原样
    if(origin == ORIGIN_TOP_LEFT || origin == ORIGIN_BOTTOM_LEFT) {
        return x;
    }
    // 原点在右上或右下：翻转X轴
    return width - 1 - x;
}
 
/******************************************************************************
      函数说明：根据原点位置转换Y坐标
      入口数据：y        区域内的Y坐标 (0=顶部，height-1=底部)
                height   区域高度
                origin   原点位置
      返回值：  转换后的相对Y坐标
******************************************************************************/
static u16 transform_y(u16 y, u16 height, u8 origin) {
    // 原点在左上或右上：保持原样
    if(origin == ORIGIN_TOP_LEFT || origin == ORIGIN_TOP_RIGHT) {
        return y;
    }
    // 原点在左下或右下：翻转Y轴
    return height - 1 - y;
}